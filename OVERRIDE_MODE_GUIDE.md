# Chrome 插件覆盖模式功能说明

## 🎯 功能概述

Chrome 插件现在支持**覆盖模式**，当用户在网页上多次选择文本并右键点击"保存到 flomo"菜单项时，每次新的文本选择会完全替换（覆盖）侧边栏中当前显示的内容，而不是追加到现有内容后面。

## ✨ 主要特性

### 1. 完全覆盖
- 每次新的文本选择都会完全替换侧边栏中的内容
- 不会出现内容追加或混合的情况
- 确保侧边栏始终显示最新选择的内容

### 2. 实时更新
- 使用 Chrome Storage API 的变化监听器
- 当侧边栏已经打开时，新的文本选择会立即更新显示
- 无需手动刷新或重新打开侧边栏

### 3. 视觉反馈
- 内容更新时会有短暂的蓝色高亮效果
- 自动聚焦到文本区域，方便用户编辑
- 清除之前的状态消息

### 4. 页面信息同步
- 每次更新都会同步显示新的页面标题和URL
- 如果没有页面信息，会自动隐藏信息区域

## 🔧 技术实现

### 修改的文件

1. **sidepanel.js**
   - 增强了 `loadPendingContent()` 函数，添加覆盖模式逻辑
   - 新增 `setupStorageListener()` 函数，监听存储变化
   - 添加视觉反馈和用户体验改进

2. **background.js**
   - 为每次保存添加唯一标识符，确保存储变化事件正确触发
   - 优化注释，明确覆盖模式的意图

### 核心逻辑

```javascript
// 监听存储变化，实现实时内容更新
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local' && changes.pendingContent) {
    if (changes.pendingContent.newValue) {
      // 添加视觉反馈
      contentText.style.backgroundColor = '#e8f0fe';
      
      // 覆盖当前内容
      loadPendingContent();
      
      // 恢复正常样式
      setTimeout(() => {
        contentText.style.backgroundColor = '';
      }, 500);
    }
  }
});
```

## 📋 使用方法

### 基本操作
1. 在任意网页上选择文本
2. 右键点击"保存到 Flomo"
3. 侧边栏打开并显示选中的内容
4. 选择其他文本并重复步骤2
5. 观察侧边栏中的内容被完全替换

### 测试步骤
1. 打开 `test-override-mode.html` 测试页面
2. 按照页面上的指示进行测试
3. 验证每次新选择都能正确覆盖之前的内容

## ✅ 预期行为

### 正确的覆盖模式
- ✅ 第一次选择文本：侧边栏显示选中内容
- ✅ 第二次选择文本：侧边栏内容被完全替换
- ✅ 后续选择：每次都完全替换，不追加

### 用户体验
- ✅ 内容更新时有视觉反馈（蓝色高亮）
- ✅ 文本区域自动获得焦点
- ✅ 页面信息正确更新
- ✅ 状态消息被清除

## 🐛 故障排除

### 如果内容被追加而不是替换
- 检查 `setupStorageListener()` 是否正确调用
- 确认 Chrome Storage API 权限正常
- 查看浏览器控制台是否有错误信息

### 如果侧边栏没有自动更新
- 确认侧边栏已经打开
- 检查存储监听器是否正确设置
- 验证 `pendingContent` 是否正确存储

### 如果视觉反馈不工作
- 检查 CSS 样式是否被其他规则覆盖
- 确认 setTimeout 逻辑正常执行

## 🔄 版本兼容性

- Chrome Manifest V3
- Chrome Storage API
- Chrome Side Panel API
- Chrome Context Menus API

## 📝 注意事项

1. **存储清理**：每次加载内容后会自动清理临时存储，避免数据累积
2. **错误处理**：包含完整的错误处理和用户提示
3. **性能优化**：使用事件监听器而不是轮询，确保高效的实时更新
4. **用户体验**：添加视觉反馈和自动聚焦，提升操作体验

## 🚀 未来改进

- 可考虑添加用户设置，允许选择覆盖模式或追加模式
- 可以添加撤销功能，允许用户恢复之前的内容
- 可以考虑添加内容历史记录功能
